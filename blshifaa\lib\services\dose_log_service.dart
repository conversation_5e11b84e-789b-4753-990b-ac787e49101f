import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class DoseLogService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة سجل جرعة جديد
  Future<int> addDoseLog(DoseLog doseLog) async {
    final db = await _databaseHelper.database;
    return await db.insert('dose_logs', doseLog.toMap());
  }

  // تسجيل جرعة تم أخذها
  Future<int> logTakenDose({
    required int medicineId,
    required String medicineName,
    required int patientId,
    required DateTime scheduledDateTime,
    DateTime? actualDateTime,
  }) async {
    final doseLog = DoseLog(
      medicineId: medicineId,
      medicineName: medicineName,
      patientId: patientId,
      scheduledDateTime: scheduledDateTime,
      actualDateTime: actualDateTime ?? DateTime.now(),
      status: 'taken',
      createdAt: DateTime.now(),
    );
    
    return await addDoseLog(doseLog);
  }

  // تسجيل جرعة مفقودة
  Future<int> logMissedDose({
    required int medicineId,
    required String medicineName,
    required int patientId,
    required DateTime scheduledDateTime,
  }) async {
    final doseLog = DoseLog(
      medicineId: medicineId,
      medicineName: medicineName,
      patientId: patientId,
      scheduledDateTime: scheduledDateTime,
      status: 'missed',
      createdAt: DateTime.now(),
    );
    
    return await addDoseLog(doseLog);
  }

  // الحصول على سجل الجرعات لمريض معين
  Future<List<DoseLog>> getDoseLogsByPatientId(int patientId, {int? limit}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dose_logs',
      where: 'patient_id = ?',
      whereArgs: [patientId],
      orderBy: 'scheduled_datetime DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => DoseLog.fromMap(maps[i]));
  }

  // الحصول على سجل الجرعات لدواء معين
  Future<List<DoseLog>> getDoseLogsByMedicineId(int medicineId, {int? limit}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dose_logs',
      where: 'medicine_id = ?',
      whereArgs: [medicineId],
      orderBy: 'scheduled_datetime DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => DoseLog.fromMap(maps[i]));
  }

  // الحصول على سجل الجرعات لفترة معينة
  Future<List<DoseLog>> getDoseLogsByDateRange({
    required int patientId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dose_logs',
      where: 'patient_id = ? AND scheduled_datetime >= ? AND scheduled_datetime <= ?',
      whereArgs: [patientId, startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'scheduled_datetime DESC',
    );
    return List.generate(maps.length, (i) => DoseLog.fromMap(maps[i]));
  }

  // الحصول على إحصائيات الجرعات لمريض معين
  Future<Map<String, int>> getDoseStatistics(int patientId, {DateTime? startDate, DateTime? endDate}) async {
    final db = await _databaseHelper.database;
    
    String whereClause = 'patient_id = ?';
    List<dynamic> whereArgs = [patientId];
    
    if (startDate != null && endDate != null) {
      whereClause += ' AND scheduled_datetime >= ? AND scheduled_datetime <= ?';
      whereArgs.addAll([startDate.toIso8601String(), endDate.toIso8601String()]);
    }

    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        status,
        COUNT(*) as count
      FROM dose_logs 
      WHERE $whereClause
      GROUP BY status
    ''', whereArgs);

    Map<String, int> statistics = {
      'taken': 0,
      'missed': 0,
      'pending': 0,
      'total': 0,
    };

    for (var row in result) {
      statistics[row['status']] = row['count'];
      statistics['total'] = statistics['total']! + (row['count'] as int);
    }

    return statistics;
  }

  // الحصول على معدل الالتزام بالدواء
  Future<double> getComplianceRate(int patientId, {DateTime? startDate, DateTime? endDate}) async {
    final statistics = await getDoseStatistics(patientId, startDate: startDate, endDate: endDate);
    final total = statistics['total']!;
    final taken = statistics['taken']!;
    
    if (total == 0) return 0.0;
    return (taken / total) * 100;
  }

  // الحصول على سجل الجرعات لليوم الحالي
  Future<List<DoseLog>> getTodayDoseLogs(int patientId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(Duration(days: 1));
    
    return await getDoseLogsByDateRange(
      patientId: patientId,
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  // البحث في سجل الجرعات
  Future<List<DoseLog>> searchDoseLogs({
    required int patientId,
    String? medicineName,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _databaseHelper.database;
    
    String whereClause = 'patient_id = ?';
    List<dynamic> whereArgs = [patientId];
    
    if (medicineName != null && medicineName.isNotEmpty) {
      whereClause += ' AND medicine_name LIKE ?';
      whereArgs.add('%$medicineName%');
    }
    
    if (status != null && status.isNotEmpty) {
      whereClause += ' AND status = ?';
      whereArgs.add(status);
    }
    
    if (startDate != null && endDate != null) {
      whereClause += ' AND scheduled_datetime >= ? AND scheduled_datetime <= ?';
      whereArgs.addAll([startDate.toIso8601String(), endDate.toIso8601String()]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'dose_logs',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'scheduled_datetime DESC',
    );
    
    return List.generate(maps.length, (i) => DoseLog.fromMap(maps[i]));
  }

  // حذف سجل الجرعات لدواء معين
  Future<int> deleteDoseLogsByMedicineId(int medicineId) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'dose_logs',
      where: 'medicine_id = ?',
      whereArgs: [medicineId],
    );
  }

  // حذف سجل الجرعات لمريض معين
  Future<int> deleteDoseLogsByPatientId(int patientId) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'dose_logs',
      where: 'patient_id = ?',
      whereArgs: [patientId],
    );
  }
}
