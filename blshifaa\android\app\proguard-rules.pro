# قواعد ProGuard لتطبيق بالشفا

# Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# SQLite
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Local Notifications
-keep class com.dexterous.** { *; }

# Workmanager
-keep class be.tramckrijte.workmanager.** { *; }

# تجنب تحذيرات ProGuard
-dontwarn io.flutter.embedding.**
-dontwarn org.sqlite.**
