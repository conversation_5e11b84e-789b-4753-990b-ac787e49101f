import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'blshifaa.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // جدول المرضى
    await db.execute('''
      CREATE TABLE patients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول الأدوية
    await db.execute('''
      CREATE TABLE medicines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        patient_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        dosage TEXT NOT NULL,
        doses_per_day INTEGER NOT NULL,
        hours_between_doses INTEGER NOT NULL,
        treatment_duration_days INTEGER,
        first_dose_datetime TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE CASCADE
      )
    ''');

    // جدول جدولة الجرعات
    await db.execute('''
      CREATE TABLE dosage_schedules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medicine_id INTEGER NOT NULL,
        scheduled_datetime TEXT NOT NULL,
        is_taken INTEGER DEFAULT 0,
        taken_datetime TEXT,
        is_missed INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        FOREIGN KEY (medicine_id) REFERENCES medicines (id) ON DELETE CASCADE
      )
    ''');

    // جدول أسماء الأدوية المحفوظة
    await db.execute('''
      CREATE TABLE saved_medicine_names (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        usage_count INTEGER DEFAULT 1,
        last_used TEXT NOT NULL
      )
    ''');

    // جدول سجل الجرعات
    await db.execute('''
      CREATE TABLE dose_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medicine_id INTEGER NOT NULL,
        medicine_name TEXT NOT NULL,
        patient_id INTEGER NOT NULL,
        scheduled_datetime TEXT NOT NULL,
        actual_datetime TEXT,
        status TEXT NOT NULL, -- 'taken', 'missed', 'pending'
        created_at TEXT NOT NULL,
        FOREIGN KEY (medicine_id) REFERENCES medicines (id) ON DELETE CASCADE,
        FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('CREATE INDEX idx_medicines_patient_id ON medicines(patient_id)');
    await db.execute('CREATE INDEX idx_dosage_schedules_medicine_id ON dosage_schedules(medicine_id)');
    await db.execute('CREATE INDEX idx_dosage_schedules_datetime ON dosage_schedules(scheduled_datetime)');
    await db.execute('CREATE INDEX idx_dose_logs_patient_id ON dose_logs(patient_id)');
    await db.execute('CREATE INDEX idx_dose_logs_medicine_id ON dose_logs(medicine_id)');
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // حذف قاعدة البيانات (للاختبار فقط)
  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'blshifaa.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
