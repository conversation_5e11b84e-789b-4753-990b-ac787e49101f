import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class SavedMedicineNameService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة أو تحديث اسم دواء محفوظ
  Future<int> saveOrUpdateMedicineName(String name) async {
    final db = await _databaseHelper.database;
    
    // البحث عن الاسم الموجود
    final List<Map<String, dynamic>> existing = await db.query(
      'saved_medicine_names',
      where: 'name = ?',
      whereArgs: [name.trim()],
    );

    if (existing.isNotEmpty) {
      // تحديث عدد الاستخدام وتاريخ آخر استخدام
      final savedName = SavedMedicineName.fromMap(existing.first);
      final updated = savedName.copyWith(
        usageCount: savedName.usageCount + 1,
        lastUsed: DateTime.now(),
      );
      
      return await db.update(
        'saved_medicine_names',
        updated.toMap(),
        where: 'id = ?',
        whereArgs: [updated.id],
      );
    } else {
      // إضافة اسم جديد
      final newName = SavedMedicineName(
        name: name.trim(),
        usageCount: 1,
        lastUsed: DateTime.now(),
      );
      
      return await db.insert('saved_medicine_names', newName.toMap());
    }
  }

  // البحث عن أسماء الأدوية بناءً على النص المدخل
  Future<List<String>> searchMedicineNames(String query, {int limit = 10}) async {
    if (query.trim().isEmpty) return [];
    
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'saved_medicine_names',
      where: 'name LIKE ?',
      whereArgs: ['${query.trim()}%'], // البحث بالأحرف الأولى
      orderBy: 'usage_count DESC, last_used DESC', // ترتيب حسب الاستخدام والتاريخ
      limit: limit,
    );
    
    return maps.map((map) => map['name'] as String).toList();
  }

  // الحصول على جميع أسماء الأدوية المحفوظة
  Future<List<SavedMedicineName>> getAllSavedMedicineNames() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'saved_medicine_names',
      orderBy: 'usage_count DESC, last_used DESC',
    );
    return List.generate(maps.length, (i) => SavedMedicineName.fromMap(maps[i]));
  }

  // الحصول على أكثر الأدوية استخداماً
  Future<List<SavedMedicineName>> getMostUsedMedicineNames({int limit = 20}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'saved_medicine_names',
      orderBy: 'usage_count DESC, last_used DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => SavedMedicineName.fromMap(maps[i]));
  }

  // الحصول على الأدوية المستخدمة مؤخراً
  Future<List<SavedMedicineName>> getRecentlyUsedMedicineNames({int limit = 20}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'saved_medicine_names',
      orderBy: 'last_used DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => SavedMedicineName.fromMap(maps[i]));
  }

  // حذف اسم دواء محفوظ
  Future<int> deleteSavedMedicineName(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'saved_medicine_names',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // حذف الأسماء غير المستخدمة لفترة طويلة
  Future<int> cleanupOldMedicineNames({int daysOld = 365}) async {
    final db = await _databaseHelper.database;
    final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
    
    return await db.delete(
      'saved_medicine_names',
      where: 'last_used < ? AND usage_count = 1',
      whereArgs: [cutoffDate.toIso8601String()],
    );
  }

  // الحصول على عدد أسماء الأدوية المحفوظة
  Future<int> getSavedMedicineNamesCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM saved_medicine_names');
    return result.first['count'] as int;
  }

  // البحث المتقدم مع اقتراحات ذكية
  Future<List<String>> getSmartSuggestions(String query, {int limit = 5}) async {
    if (query.trim().isEmpty) return [];
    
    final db = await _databaseHelper.database;
    
    // البحث بالأحرف الأولى أولاً
    List<Map<String, dynamic>> exactMatches = await db.query(
      'saved_medicine_names',
      where: 'name LIKE ?',
      whereArgs: ['${query.trim()}%'],
      orderBy: 'usage_count DESC, last_used DESC',
      limit: limit,
    );
    
    List<String> suggestions = exactMatches.map((map) => map['name'] as String).toList();
    
    // إذا لم نحصل على عدد كافي من النتائج، نبحث في أي مكان في الاسم
    if (suggestions.length < limit) {
      List<Map<String, dynamic>> partialMatches = await db.query(
        'saved_medicine_names',
        where: 'name LIKE ? AND name NOT LIKE ?',
        whereArgs: ['%${query.trim()}%', '${query.trim()}%'],
        orderBy: 'usage_count DESC, last_used DESC',
        limit: limit - suggestions.length,
      );
      
      suggestions.addAll(partialMatches.map((map) => map['name'] as String));
    }
    
    return suggestions;
  }
}
