class SavedMedicineName {
  final int? id;
  final String name;
  final int usageCount;
  final DateTime lastUsed;

  SavedMedicineName({
    this.id,
    required this.name,
    this.usageCount = 1,
    required this.lastUsed,
  });

  // تحويل من Map إلى SavedMedicineName
  factory SavedMedicineName.fromMap(Map<String, dynamic> map) {
    return SavedMedicineName(
      id: map['id'],
      name: map['name'],
      usageCount: map['usage_count'],
      lastUsed: DateTime.parse(map['last_used']),
    );
  }

  // تحويل من SavedMedicineName إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'usage_count': usageCount,
      'last_used': lastUsed.toIso8601String(),
    };
  }

  // نسخ مع تعديل بعض الخصائص
  SavedMedicineName copyWith({
    int? id,
    String? name,
    int? usageCount,
    DateTime? lastUsed,
  }) {
    return SavedMedicineName(
      id: id ?? this.id,
      name: name ?? this.name,
      usageCount: usageCount ?? this.usageCount,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  @override
  String toString() {
    return 'SavedMedicineName{id: $id, name: $name, usageCount: $usageCount, lastUsed: $lastUsed}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SavedMedicineName &&
        other.id == id &&
        other.name == name;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode;
  }
}
