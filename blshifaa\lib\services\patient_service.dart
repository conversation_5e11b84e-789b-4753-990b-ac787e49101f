import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class PatientService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة مريض جديد
  Future<int> addPatient(Patient patient) async {
    final db = await _databaseHelper.database;
    return await db.insert('patients', patient.toMap());
  }

  // الحصول على جميع المرضى
  Future<List<Patient>> getAllPatients() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'patients',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Patient.fromMap(maps[i]));
  }

  // الحصول على مريض بالمعرف
  Future<Patient?> getPatientById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'patients',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Patient.fromMap(maps.first);
    }
    return null;
  }

  // تحديث مريض
  Future<int> updatePatient(Patient patient) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'patients',
      patient.toMap(),
      where: 'id = ?',
      whereArgs: [patient.id],
    );
  }

  // حذف مريض
  Future<int> deletePatient(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'patients',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // البحث عن المرضى بالاسم
  Future<List<Patient>> searchPatients(String name) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'patients',
      where: 'name LIKE ?',
      whereArgs: ['%$name%'],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Patient.fromMap(maps[i]));
  }

  // عدد المرضى
  Future<int> getPatientsCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM patients');
    return result.first['count'] as int;
  }
}
