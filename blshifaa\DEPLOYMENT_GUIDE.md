# دليل نشر تطبيق بالشفا 🚀

## 📱 نشر التطبيق على Android

### المتطلبات:
- Android Studio مثبت
- Android SDK مع API level 21 أو أحدث
- Java Development Kit (JDK) 8 أو أحدث

### خطوات النشر:

#### 1. إنشاء Keystore للتوقيع:
```bash
keytool -genkey -v -keystore blshifaa-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias blshifaa-key
```

#### 2. إعداد ملف key.properties:
```properties
storePassword=your_store_password
keyPassword=your_key_password
keyAlias=blshifaa-key
storeFile=../blshifaa-keystore.jks
```

#### 3. بناء APK للنشر:
```bash
flutter build apk --release
```

#### 4. بناء App Bundle (مفضل لـ Google Play):
```bash
flutter build appbundle --release
```

### الملفات المُنتجة:
- **APK**: `build/app/outputs/flutter-apk/app-release.apk`
- **App Bundle**: `build/app/outputs/bundle/release/app-release.aab`

### رفع على Google Play Store:
1. إنشاء حساب مطور على Google Play Console
2. إنشاء تطبيق جديد
3. رفع App Bundle (.aab)
4. إضافة وصف التطبيق والصور
5. إعداد السعر والتوزيع
6. مراجعة ونشر التطبيق

---

## 🍎 نشر التطبيق على iOS

### المتطلبات:
- macOS مع Xcode 12 أو أحدث
- حساب مطور Apple ($99/سنة)
- iPhone أو iPad للاختبار

### خطوات النشر:

#### 1. إعداد Bundle Identifier:
```bash
# في ملف ios/Runner.xcodeproj
PRODUCT_BUNDLE_IDENTIFIER = com.blshifaa.app
```

#### 2. إعداد التوقيع في Xcode:
- فتح `ios/Runner.xcworkspace` في Xcode
- اختيار Team في Signing & Capabilities
- إعداد Provisioning Profile

#### 3. بناء التطبيق:
```bash
flutter build ios --release
```

#### 4. أرشفة التطبيق في Xcode:
- Product → Archive
- اختيار "Distribute App"
- اختيار "App Store Connect"

### رفع على App Store:
1. إنشاء تطبيق في App Store Connect
2. رفع البناء من Xcode أو Application Loader
3. إضافة معلومات التطبيق والصور
4. إرسال للمراجعة
5. نشر التطبيق بعد الموافقة

---

## 🌐 نشر التطبيق على الويب

### بناء نسخة الويب:
```bash
flutter build web --release
```

### النشر على Firebase Hosting:
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# إعداد المشروع
firebase init hosting

# النشر
firebase deploy
```

### النشر على GitHub Pages:
```bash
# إضافة web/ إلى repository
git add build/web/
git commit -m "Deploy web version"
git subtree push --prefix build/web origin gh-pages
```

---

## 📋 قائمة التحقق قبل النشر

### Android:
- [ ] تحديث versionCode و versionName
- [ ] اختبار التطبيق على أجهزة مختلفة
- [ ] إضافة أيقونة التطبيق
- [ ] إعداد الأذونات المطلوبة
- [ ] اختبار الإشعارات
- [ ] تحسين حجم التطبيق

### iOS:
- [ ] تحديث CFBundleVersion و CFBundleShortVersionString
- [ ] اختبار على iPhone و iPad
- [ ] إضافة أيقونة التطبيق لجميع الأحجام
- [ ] إعداد Launch Screen
- [ ] اختبار الإشعارات
- [ ] مراجعة App Store Guidelines

### عام:
- [ ] اختبار جميع الوظائف
- [ ] مراجعة النصوص والترجمة
- [ ] تحسين الأداء
- [ ] إعداد Analytics
- [ ] إعداد Crash Reporting
- [ ] كتابة وصف التطبيق
- [ ] إعداد لقطات الشاشة
- [ ] مراجعة سياسة الخصوصية

---

## 🔧 إعدادات إضافية

### تحسين حجم التطبيق:
```bash
# Android
flutter build apk --release --shrink

# تفعيل ProGuard
android {
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
        }
    }
}
```

### إعداد الإشعارات:
- Android: إضافة google-services.json
- iOS: إضافة GoogleService-Info.plist
- إعداد Firebase Cloud Messaging

### Analytics:
```bash
# إضافة Firebase Analytics
flutter pub add firebase_analytics
```

---

## 📞 الدعم والمساعدة

للحصول على المساعدة في النشر:
- مراجعة وثائق Flutter الرسمية
- التواصل مع فريق التطوير
- مراجعة أدلة Google Play و App Store

**ملاحظة**: تأكد من مراجعة جميع القوانين واللوائح المحلية للتطبيقات الطبية قبل النشر.
