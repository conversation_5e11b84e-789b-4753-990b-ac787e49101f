#!/bin/bash

# سكريبت بناء ونشر تطبيق بالشفا
# Build and Deploy Script for Blshifaa App

echo "🚀 بدء عملية بناء تطبيق بالشفا..."
echo "🚀 Starting Blshifaa App Build Process..."

# تنظيف المشروع
echo "🧹 تنظيف المشروع..."
flutter clean
flutter pub get

# بناء نسخة الويب
echo "🌐 بناء نسخة الويب..."
flutter build web --release
if [ $? -eq 0 ]; then
    echo "✅ تم بناء نسخة الويب بنجاح"
    echo "📁 الملفات في: build/web/"
else
    echo "❌ فشل في بناء نسخة الويب"
fi

# بناء APK للأندرويد
echo "📱 بناء APK للأندرويد..."
flutter build apk --release
if [ $? -eq 0 ]; then
    echo "✅ تم بناء APK بنجاح"
    echo "📁 الملف في: build/app/outputs/flutter-apk/app-release.apk"
else
    echo "❌ فشل في بناء APK"
fi

# بناء App Bundle للأندرويد
echo "📦 بناء App Bundle للأندرويد..."
flutter build appbundle --release
if [ $? -eq 0 ]; then
    echo "✅ تم بناء App Bundle بنجاح"
    echo "📁 الملف في: build/app/outputs/bundle/release/app-release.aab"
else
    echo "❌ فشل في بناء App Bundle"
fi

# بناء نسخة iOS (إذا كان النظام macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 بناء نسخة iOS..."
    flutter build ios --release --no-codesign
    if [ $? -eq 0 ]; then
        echo "✅ تم بناء نسخة iOS بنجاح"
        echo "📁 الملفات في: build/ios/iphoneos/"
    else
        echo "❌ فشل في بناء نسخة iOS"
    fi
else
    echo "⚠️  بناء iOS متاح فقط على macOS"
fi

# عرض ملخص النتائج
echo ""
echo "📊 ملخص النتائج:"
echo "=================="

if [ -f "build/web/index.html" ]; then
    echo "✅ نسخة الويب: متاحة"
    WEB_SIZE=$(du -sh build/web | cut -f1)
    echo "   الحجم: $WEB_SIZE"
fi

if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
    echo "✅ APK للأندرويد: متاح"
    APK_SIZE=$(du -sh build/app/outputs/flutter-apk/app-release.apk | cut -f1)
    echo "   الحجم: $APK_SIZE"
fi

if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
    echo "✅ App Bundle للأندرويد: متاح"
    AAB_SIZE=$(du -sh build/app/outputs/bundle/release/app-release.aab | cut -f1)
    echo "   الحجم: $AAB_SIZE"
fi

echo ""
echo "🎉 انتهت عملية البناء!"
echo "🎉 Build process completed!"

# إنشاء مجلد للتوزيع
DIST_DIR="dist/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$DIST_DIR"

# نسخ الملفات المبنية
if [ -d "build/web" ]; then
    cp -r build/web "$DIST_DIR/web"
    echo "📁 نسخة الويب منسوخة إلى: $DIST_DIR/web"
fi

if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
    cp build/app/outputs/flutter-apk/app-release.apk "$DIST_DIR/blshifaa-android.apk"
    echo "📁 APK منسوخ إلى: $DIST_DIR/blshifaa-android.apk"
fi

if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
    cp build/app/outputs/bundle/release/app-release.aab "$DIST_DIR/blshifaa-android.aab"
    echo "📁 App Bundle منسوخ إلى: $DIST_DIR/blshifaa-android.aab"
fi

echo ""
echo "📦 جميع الملفات متاحة في: $DIST_DIR"
echo "📦 All files available in: $DIST_DIR"
