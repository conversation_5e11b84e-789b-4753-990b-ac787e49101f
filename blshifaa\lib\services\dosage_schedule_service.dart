import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class DosageScheduleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إنشاء جدولة الجرعات للدواء
  Future<List<int>> createDosageSchedule(Medicine medicine) async {
    final db = await _databaseHelper.database;
    List<int> scheduleIds = [];

    // حساب عدد الأيام الإجمالية
    int totalDays = medicine.treatmentDurationDays ?? 30; // افتراضي 30 يوم إذا لم يحدد
    
    DateTime currentDateTime = medicine.firstDoseDateTime;
    DateTime endDate = medicine.firstDoseDateTime.add(Duration(days: totalDays));

    while (currentDateTime.isBefore(endDate)) {
      // إنشاء جرعات اليوم الواحد
      for (int i = 0; i < medicine.dosesPerDay; i++) {
        DateTime doseTime = currentDateTime.add(Duration(hours: medicine.hoursBetweenDoses * i));
        
        // التأكد من أن الجرعة لا تتجاوز تاريخ انتهاء العلاج
        if (doseTime.isBefore(endDate)) {
          DosageSchedule schedule = DosageSchedule(
            medicineId: medicine.id!,
            scheduledDateTime: doseTime,
            createdAt: DateTime.now(),
          );
          
          int id = await db.insert('dosage_schedules', schedule.toMap());
          scheduleIds.add(id);
        }
      }
      
      // الانتقال لليوم التالي
      currentDateTime = currentDateTime.add(Duration(days: 1));
      // إعادة تعيين الوقت لنفس وقت الجرعة الأولى
      currentDateTime = DateTime(
        currentDateTime.year,
        currentDateTime.month,
        currentDateTime.day,
        medicine.firstDoseDateTime.hour,
        medicine.firstDoseDateTime.minute,
      );
    }

    return scheduleIds;
  }

  // الحصول على جدولة الجرعات لدواء معين
  Future<List<DosageSchedule>> getSchedulesByMedicineId(int medicineId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dosage_schedules',
      where: 'medicine_id = ?',
      whereArgs: [medicineId],
      orderBy: 'scheduled_datetime ASC',
    );
    return List.generate(maps.length, (i) => DosageSchedule.fromMap(maps[i]));
  }

  // الحصول على الجرعات المجدولة لليوم الحالي
  Future<List<DosageSchedule>> getTodaySchedules({int? patientId}) async {
    final db = await _databaseHelper.database;
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(Duration(days: 1));

    String query = '''
      SELECT ds.* FROM dosage_schedules ds
      INNER JOIN medicines m ON ds.medicine_id = m.id
      WHERE ds.scheduled_datetime >= ? AND ds.scheduled_datetime < ?
      AND m.is_active = 1
    ''';
    
    List<dynamic> args = [startOfDay.toIso8601String(), endOfDay.toIso8601String()];
    
    if (patientId != null) {
      query += ' AND m.patient_id = ?';
      args.add(patientId);
    }
    
    query += ' ORDER BY ds.scheduled_datetime ASC';

    final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);
    return List.generate(maps.length, (i) => DosageSchedule.fromMap(maps[i]));
  }

  // الحصول على الجرعات القادمة (خلال الساعات القليلة القادمة)
  Future<List<DosageSchedule>> getUpcomingSchedules({int? patientId, int hours = 4}) async {
    final db = await _databaseHelper.database;
    final now = DateTime.now();
    final futureTime = now.add(Duration(hours: hours));

    String query = '''
      SELECT ds.* FROM dosage_schedules ds
      INNER JOIN medicines m ON ds.medicine_id = m.id
      WHERE ds.scheduled_datetime >= ? AND ds.scheduled_datetime <= ?
      AND ds.is_taken = 0 AND m.is_active = 1
    ''';
    
    List<dynamic> args = [now.toIso8601String(), futureTime.toIso8601String()];
    
    if (patientId != null) {
      query += ' AND m.patient_id = ?';
      args.add(patientId);
    }
    
    query += ' ORDER BY ds.scheduled_datetime ASC';

    final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);
    return List.generate(maps.length, (i) => DosageSchedule.fromMap(maps[i]));
  }

  // تسجيل أخذ الجرعة
  Future<int> markDoseAsTaken(int scheduleId) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'dosage_schedules',
      {
        'is_taken': 1,
        'taken_datetime': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [scheduleId],
    );
  }

  // تسجيل الجرعة كمفقودة
  Future<int> markDoseAsMissed(int scheduleId) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'dosage_schedules',
      {'is_missed': 1},
      where: 'id = ?',
      whereArgs: [scheduleId],
    );
  }

  // الحصول على الجرعات المتأخرة
  Future<List<DosageSchedule>> getOverdueSchedules({int? patientId}) async {
    final db = await _databaseHelper.database;
    final oneHourAgo = DateTime.now().subtract(Duration(hours: 1));

    String query = '''
      SELECT ds.* FROM dosage_schedules ds
      INNER JOIN medicines m ON ds.medicine_id = m.id
      WHERE ds.scheduled_datetime < ? 
      AND ds.is_taken = 0 AND ds.is_missed = 0 
      AND m.is_active = 1
    ''';
    
    List<dynamic> args = [oneHourAgo.toIso8601String()];
    
    if (patientId != null) {
      query += ' AND m.patient_id = ?';
      args.add(patientId);
    }
    
    query += ' ORDER BY ds.scheduled_datetime ASC';

    final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);
    return List.generate(maps.length, (i) => DosageSchedule.fromMap(maps[i]));
  }

  // حذف جدولة الجرعات لدواء معين
  Future<int> deleteSchedulesByMedicineId(int medicineId) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'dosage_schedules',
      where: 'medicine_id = ?',
      whereArgs: [medicineId],
    );
  }

  // تحديث الجرعات المتأخرة تلقائياً
  Future<int> updateOverdueSchedules() async {
    final db = await _databaseHelper.database;
    final oneHourAgo = DateTime.now().subtract(Duration(hours: 1));
    
    return await db.update(
      'dosage_schedules',
      {'is_missed': 1},
      where: 'scheduled_datetime < ? AND is_taken = 0 AND is_missed = 0',
      whereArgs: [oneHourAgo.toIso8601String()],
    );
  }
}
