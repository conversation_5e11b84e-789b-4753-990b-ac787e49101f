class Medicine {
  final int? id;
  final int patientId;
  final String name;
  final String type;
  final String dosage;
  final int dosesPerDay;
  final int hoursBetweenDoses;
  final int? treatmentDurationDays;
  final DateTime firstDoseDateTime;
  final bool isActive;
  final DateTime createdAt;

  Medicine({
    this.id,
    required this.patientId,
    required this.name,
    required this.type,
    required this.dosage,
    required this.dosesPerDay,
    required this.hoursBetweenDoses,
    this.treatmentDurationDays,
    required this.firstDoseDateTime,
    this.isActive = true,
    required this.createdAt,
  });

  // تحويل من Map إلى Medicine
  factory Medicine.fromMap(Map<String, dynamic> map) {
    return Medicine(
      id: map['id'],
      patientId: map['patient_id'],
      name: map['name'],
      type: map['type'],
      dosage: map['dosage'],
      dosesPerDay: map['doses_per_day'],
      hoursBetweenDoses: map['hours_between_doses'],
      treatmentDurationDays: map['treatment_duration_days'],
      firstDoseDateTime: DateTime.parse(map['first_dose_datetime']),
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // تحويل من Medicine إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'patient_id': patientId,
      'name': name,
      'type': type,
      'dosage': dosage,
      'doses_per_day': dosesPerDay,
      'hours_between_doses': hoursBetweenDoses,
      'treatment_duration_days': treatmentDurationDays,
      'first_dose_datetime': firstDoseDateTime.toIso8601String(),
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // حساب تاريخ انتهاء العلاج
  DateTime? get endDate {
    if (treatmentDurationDays == null) return null;
    return firstDoseDateTime.add(Duration(days: treatmentDurationDays!));
  }

  // التحقق من انتهاء العلاج
  bool get isExpired {
    if (treatmentDurationDays == null) return false;
    return DateTime.now().isAfter(endDate!);
  }

  // نسخ مع تعديل بعض الخصائص
  Medicine copyWith({
    int? id,
    int? patientId,
    String? name,
    String? type,
    String? dosage,
    int? dosesPerDay,
    int? hoursBetweenDoses,
    int? treatmentDurationDays,
    DateTime? firstDoseDateTime,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return Medicine(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      name: name ?? this.name,
      type: type ?? this.type,
      dosage: dosage ?? this.dosage,
      dosesPerDay: dosesPerDay ?? this.dosesPerDay,
      hoursBetweenDoses: hoursBetweenDoses ?? this.hoursBetweenDoses,
      treatmentDurationDays: treatmentDurationDays ?? this.treatmentDurationDays,
      firstDoseDateTime: firstDoseDateTime ?? this.firstDoseDateTime,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Medicine{id: $id, name: $name, type: $type, dosage: $dosage, dosesPerDay: $dosesPerDay}';
  }
}
