import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/services.dart';

class PatientProvider with ChangeNotifier {
  final PatientService _patientService = PatientService();
  
  List<Patient> _patients = [];
  Patient? _selectedPatient;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Patient> get patients => _patients;
  Patient? get selectedPatient => _selectedPatient;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع المرضى
  Future<void> loadPatients() async {
    _setLoading(true);
    try {
      _patients = await _patientService.getAllPatients();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل المرضى: $e';
      print(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة مريض جديد
  Future<bool> addPatient(String name) async {
    if (name.trim().isEmpty) {
      _error = 'يرجى إدخال اسم المريض';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final patient = Patient(
        name: name.trim(),
        createdAt: DateTime.now(),
      );
      
      int id = await _patientService.addPatient(patient);
      if (id > 0) {
        await loadPatients(); // إعادة تحميل القائمة
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة المريض: $e';
      print(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث مريض
  Future<bool> updatePatient(Patient patient) async {
    if (patient.name.trim().isEmpty) {
      _error = 'يرجى إدخال اسم المريض';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      int result = await _patientService.updatePatient(patient);
      if (result > 0) {
        await loadPatients(); // إعادة تحميل القائمة
        if (_selectedPatient?.id == patient.id) {
          _selectedPatient = patient;
        }
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث المريض: $e';
      print(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف مريض
  Future<bool> deletePatient(int patientId) async {
    _setLoading(true);
    try {
      int result = await _patientService.deletePatient(patientId);
      if (result > 0) {
        await loadPatients(); // إعادة تحميل القائمة
        if (_selectedPatient?.id == patientId) {
          _selectedPatient = null;
        }
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف المريض: $e';
      print(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // اختيار مريض
  void selectPatient(Patient? patient) {
    _selectedPatient = patient;
    notifyListeners();
  }

  // البحث عن المرضى
  Future<List<Patient>> searchPatients(String query) async {
    try {
      if (query.trim().isEmpty) {
        return _patients;
      }
      return await _patientService.searchPatients(query);
    } catch (e) {
      _error = 'خطأ في البحث: $e';
      print(_error);
      return [];
    }
  }

  // الحصول على مريض بالمعرف
  Future<Patient?> getPatientById(int id) async {
    try {
      return await _patientService.getPatientById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على المريض: $e';
      print(_error);
      return null;
    }
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // إعادة تعيين البيانات
  void reset() {
    _patients = [];
    _selectedPatient = null;
    _isLoading = false;
    _error = null;
    notifyListeners();
  }
}
