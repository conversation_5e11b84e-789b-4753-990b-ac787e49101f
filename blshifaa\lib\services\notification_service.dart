import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:workmanager/workmanager.dart';  // مؤقتاً معطل
import 'dart:io';
import '../models/models.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  
  NotificationService._internal();
  
  factory NotificationService() => _instance;

  // إعداد الإشعارات
  Future<void> initialize() async {
    // إعدادات Android
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // إعدادات iOS
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // إعدادات Windows
    const LinuxInitializationSettings linuxSettings = LinuxInitializationSettings(
      defaultActionName: 'Open notification',
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
      linux: linuxSettings,
    );

    await _notifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // طلب الأذونات
    await _requestPermissions();

    // إعداد Workmanager للمهام في الخلفية - مؤقتاً معطل
    // if (Platform.isAndroid || Platform.isIOS) {
    //   await Workmanager().initialize(
    //     callbackDispatcher,
    //     isInDebugMode: false,
    //   );
    // }
  }

  // طلب الأذونات
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      
      await androidImplementation?.requestNotificationsPermission();
      await androidImplementation?.requestExactAlarmsPermission();
    } else if (Platform.isIOS) {
      final IOSFlutterLocalNotificationsPlugin? iosImplementation =
          _notifications.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>();
      
      await iosImplementation?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
  }

  // جدولة إشعار لجرعة دواء (مبسط للاختبار)
  Future<void> scheduleDoseNotification({
    required int id,
    required String medicineName,
    required String dosage,
    required DateTime scheduledTime,
    required String patientName,
  }) async {
    // للتبسيط، سنعرض إشعار فوري فقط في هذا الإصدار
    print('تم جدولة إشعار للجرعة: $medicineName في $scheduledTime');

    // يمكن إضافة منطق الجدولة الفعلي لاحقاً
    // await _notifications.schedule(...);
  }

  // إشعار فوري لجرعة حان وقتها
  Future<void> showDoseNotification({
    required int id,
    required String medicineName,
    required String dosage,
    required String patientName,
  }) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'dose_alerts',
      'تنبيه الجرعات',
      channelDescription: 'تنبيهات فورية لمواعيد الجرعات',
      importance: Importance.max,
      priority: Priority.max,
      showWhen: true,
      enableVibration: true,
      playSound: true,
      ongoing: true,
      autoCancel: false,
      category: AndroidNotificationCategory.alarm,
      actions: [
        AndroidNotificationAction(
          'take_dose',
          'تم أخذ الجرعة ✓',
          showsUserInterface: true,
        ),
        AndroidNotificationAction(
          'snooze',
          'تأجيل 10 دقائق',
          showsUserInterface: false,
        ),
      ],
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.critical,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      id,
      'موعد الجرعة - $medicineName',
      'حان وقت تناول $dosage من $medicineName للمريض $patientName',
      details,
      payload: 'dose_$id',
    );
  }

  // إلغاء إشعار معين
  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  // إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // الحصول على الإشعارات المجدولة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  // معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null && payload.startsWith('dose_')) {
      final doseId = int.tryParse(payload.substring(5));
      if (doseId != null) {
        // يمكن إضافة منطق للتنقل إلى شاشة معينة
        print('تم النقر على إشعار الجرعة: $doseId');
      }
    }
  }

  // جدولة إشعارات متعددة لدواء
  Future<void> scheduleAllDoseNotifications(Medicine medicine, List<DosageSchedule> schedules) async {
    for (DosageSchedule schedule in schedules) {
      await scheduleDoseNotification(
        id: schedule.id!,
        medicineName: medicine.name,
        dosage: medicine.dosage,
        scheduledTime: schedule.scheduledDateTime,
        patientName: '', // سيتم تحديثه من قاعدة البيانات
      );
    }
  }

  // إلغاء إشعارات دواء معين
  Future<void> cancelMedicineNotifications(List<DosageSchedule> schedules) async {
    for (DosageSchedule schedule in schedules) {
      await cancelNotification(schedule.id!);
    }
  }
}

// معالج المهام في الخلفية - مؤقتاً معطل
// @pragma('vm:entry-point')
// void callbackDispatcher() {
//   Workmanager().executeTask((task, inputData) async {
//     switch (task) {
//       case 'checkOverdueDoses':
//         // فحص الجرعات المتأخرة وإرسال إشعارات
//         await _checkOverdueDoses();
//         break;
//       case 'updateMissedDoses':
//         // تحديث الجرعات المفقودة
//         await _updateMissedDoses();
//         break;
//     }
//     return Future.value(true);
//   });
// }

// فحص الجرعات المتأخرة
Future<void> _checkOverdueDoses() async {
  // سيتم تنفيذ هذا لاحقاً
  print('فحص الجرعات المتأخرة...');
}

// تحديث الجرعات المفقودة
Future<void> _updateMissedDoses() async {
  // سيتم تنفيذ هذا لاحقاً
  print('تحديث الجرعات المفقودة...');
}
