import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../services/scheduling_service.dart';

class MedicineProvider with ChangeNotifier {
  final MedicineService _medicineService = MedicineService();
  final SchedulingService _schedulingService = SchedulingService();
  final SavedMedicineNameService _savedMedicineNameService = SavedMedicineNameService();
  
  List<Medicine> _medicines = [];
  List<String> _medicineSuggestions = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Medicine> get medicines => _medicines;
  List<String> get medicineSuggestions => _medicineSuggestions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل أدوية مريض معين
  Future<void> loadMedicinesByPatientId(int patientId) async {
    _setLoading(true);
    try {
      _medicines = await _medicineService.getMedicinesByPatientId(patientId);
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الأدوية: $e';
      print(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة دواء جديد
  Future<bool> addMedicine({
    required int patientId,
    required String name,
    required String type,
    required String dosage,
    required int dosesPerDay,
    int? treatmentDurationDays,
    required DateTime firstDoseDateTime,
  }) async {
    if (name.trim().isEmpty) {
      _error = 'يرجى إدخال اسم الدواء';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      // حساب الساعات بين الجرعات
      int hoursBetweenDoses = SchedulingService.calculateHoursBetweenDoses(dosesPerDay);
      
      final medicine = Medicine(
        patientId: patientId,
        name: name.trim(),
        type: type,
        dosage: dosage,
        dosesPerDay: dosesPerDay,
        hoursBetweenDoses: hoursBetweenDoses,
        treatmentDurationDays: treatmentDurationDays,
        firstDoseDateTime: firstDoseDateTime,
        createdAt: DateTime.now(),
      );
      
      int medicineId = await _medicineService.addMedicine(medicine);
      if (medicineId > 0) {
        // إنشاء جدولة الجرعات
        Medicine savedMedicine = medicine.copyWith(id: medicineId);
        bool scheduleCreated = await _schedulingService.createMedicineSchedule(savedMedicine);
        
        if (scheduleCreated) {
          await loadMedicinesByPatientId(patientId); // إعادة تحميل القائمة
          _error = null;
          return true;
        }
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة الدواء: $e';
      print(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف دواء
  Future<bool> deleteMedicine(int medicineId, int patientId) async {
    _setLoading(true);
    try {
      // حذف جدولة الدواء
      await _schedulingService.deleteMedicineSchedule(medicineId);
      
      // تعطيل الدواء
      int result = await _medicineService.deactivateMedicine(medicineId);
      if (result > 0) {
        await loadMedicinesByPatientId(patientId); // إعادة تحميل القائمة
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف الدواء: $e';
      print(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // البحث عن اقتراحات أسماء الأدوية
  Future<void> searchMedicineSuggestions(String query) async {
    try {
      if (query.trim().isEmpty) {
        _medicineSuggestions = [];
      } else {
        _medicineSuggestions = await _savedMedicineNameService.getSmartSuggestions(query);
      }
      notifyListeners();
    } catch (e) {
      _error = 'خطأ في البحث عن الاقتراحات: $e';
      print(_error);
    }
  }

  // مسح الاقتراحات
  void clearSuggestions() {
    _medicineSuggestions = [];
    notifyListeners();
  }

  // الحصول على الأدوية النشطة
  Future<void> loadActiveMedicines() async {
    _setLoading(true);
    try {
      _medicines = await _medicineService.getActiveMedicines();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الأدوية النشطة: $e';
      print(_error);
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على الأدوية المنتهية الصلاحية
  Future<List<Medicine>> getExpiredMedicines() async {
    try {
      return await _medicineService.getExpiredMedicines();
    } catch (e) {
      _error = 'خطأ في الحصول على الأدوية المنتهية: $e';
      print(_error);
      return [];
    }
  }

  // البحث في الأدوية
  Future<List<Medicine>> searchMedicines(String query, {int? patientId}) async {
    try {
      return await _medicineService.searchMedicines(query, patientId: patientId);
    } catch (e) {
      _error = 'خطأ في البحث: $e';
      print(_error);
      return [];
    }
  }

  // الحصول على دواء بالمعرف
  Future<Medicine?> getMedicineById(int id) async {
    try {
      return await _medicineService.getMedicineById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على الدواء: $e';
      print(_error);
      return null;
    }
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // إعادة تعيين البيانات
  void reset() {
    _medicines = [];
    _medicineSuggestions = [];
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  // الحصول على أنواع الأدوية المتاحة
  List<String> get medicineTypes => [
    'مسكن',
    'مضاد حيوي',
    'خافض حرارة',
    'مكمل غذائي',
    'فيتامين',
    'مضاد التهاب',
    'مضاد حساسية',
    'دواء ضغط',
    'دواء سكري',
    'دواء قلب',
    'أخرى',
  ];
}
