import '../models/models.dart';
import 'services.dart';
import 'notification_service.dart';

class SchedulingService {
  final DosageScheduleService _dosageScheduleService = DosageScheduleService();
  final DoseLogService _doseLogService = DoseLogService();
  final SavedMedicineNameService _savedMedicineNameService = SavedMedicineNameService();
  final NotificationService _notificationService = NotificationService();

  // حساب الساعات بين الجرعات بناءً على عدد الجرعات اليومية
  static int calculateHoursBetweenDoses(int dosesPerDay) {
    switch (dosesPerDay) {
      case 1:
        return 24; // مرة واحدة يومياً
      case 2:
        return 12; // مرتين يومياً - كل 12 ساعة
      case 3:
        return 8;  // 3 مرات يومياً - كل 8 ساعات
      case 4:
        return 6;  // 4 مرات يومياً - كل 6 ساعات
      case 5:
        return 5;  // 5 مرات يومياً - كل 5 ساعات (تقريباً)
      case 6:
        return 4;  // 6 مرات يومياً - كل 4 ساعات
      default:
        return 8;  // افتراضي
    }
  }

  // إنشاء جدولة كاملة للدواء
  Future<bool> createMedicineSchedule(Medicine medicine) async {
    try {
      // حفظ اسم الدواء في قائمة الأسماء المحفوظة
      await _savedMedicineNameService.saveOrUpdateMedicineName(medicine.name);

      // إنشاء جدولة الجرعات
      List<int> scheduleIds = await _dosageScheduleService.createDosageSchedule(medicine);

      // إنشاء سجلات الجرعات الأولية
      await _createInitialDoseLogs(medicine, scheduleIds);

      // جدولة الإشعارات
      List<DosageSchedule> schedules = await _dosageScheduleService.getSchedulesByMedicineId(medicine.id!);
      await _notificationService.scheduleAllDoseNotifications(medicine, schedules);

      return scheduleIds.isNotEmpty;
    } catch (e) {
      print('خطأ في إنشاء جدولة الدواء: $e');
      return false;
    }
  }

  // إنشاء سجلات الجرعات الأولية
  Future<void> _createInitialDoseLogs(Medicine medicine, List<int> scheduleIds) async {
    try {
      // الحصول على جدولة الجرعات المنشأة
      List<DosageSchedule> schedules = await _dosageScheduleService.getSchedulesByMedicineId(medicine.id!);

      for (DosageSchedule schedule in schedules) {
        // إنشاء سجل جرعة بحالة "pending"
        await _doseLogService.addDoseLog(
          DoseLog(
            medicineId: medicine.id!,
            medicineName: medicine.name,
            patientId: medicine.patientId,
            scheduledDateTime: schedule.scheduledDateTime,
            status: 'pending',
            createdAt: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      print('خطأ في إنشاء سجلات الجرعات الأولية: $e');
    }
  }

  // تسجيل أخذ الجرعة
  Future<bool> markDoseAsTaken(int scheduleId, {DateTime? actualTime}) async {
    try {
      // تحديث جدولة الجرعة
      await _dosageScheduleService.markDoseAsTaken(scheduleId);

      // الحصول على معلومات الجدولة
      List<DosageSchedule> schedules = await _dosageScheduleService.getSchedulesByMedicineId(scheduleId);
      if (schedules.isEmpty) return false;

      DosageSchedule schedule = schedules.first;
      
      // تسجيل الجرعة في السجل
      await _doseLogService.logTakenDose(
        medicineId: schedule.medicineId,
        medicineName: '', // سيتم تحديثه من قاعدة البيانات
        patientId: 0, // سيتم تحديثه من قاعدة البيانات
        scheduledDateTime: schedule.scheduledDateTime,
        actualDateTime: actualTime ?? DateTime.now(),
      );

      return true;
    } catch (e) {
      print('خطأ في تسجيل أخذ الجرعة: $e');
      return false;
    }
  }

  // تسجيل الجرعة كمفقودة
  Future<bool> markDoseAsMissed(int scheduleId) async {
    try {
      // تحديث جدولة الجرعة
      await _dosageScheduleService.markDoseAsMissed(scheduleId);

      // الحصول على معلومات الجدولة
      List<DosageSchedule> schedules = await _dosageScheduleService.getSchedulesByMedicineId(scheduleId);
      if (schedules.isEmpty) return false;

      DosageSchedule schedule = schedules.first;
      
      // تسجيل الجرعة المفقودة في السجل
      await _doseLogService.logMissedDose(
        medicineId: schedule.medicineId,
        medicineName: '', // سيتم تحديثه من قاعدة البيانات
        patientId: 0, // سيتم تحديثه من قاعدة البيانات
        scheduledDateTime: schedule.scheduledDateTime,
      );

      return true;
    } catch (e) {
      print('خطأ في تسجيل الجرعة المفقودة: $e');
      return false;
    }
  }

  // تحديث الجرعات المتأخرة تلقائياً
  Future<int> updateOverdueDoses() async {
    try {
      // تحديث الجرعات المتأخرة في الجدولة
      int updatedSchedules = await _dosageScheduleService.updateOverdueSchedules();

      // الحصول على الجرعات المتأخرة وتسجيلها في السجل
      List<DosageSchedule> overdueSchedules = await _dosageScheduleService.getOverdueSchedules();
      
      for (DosageSchedule schedule in overdueSchedules) {
        if (!schedule.isMissed) {
          await markDoseAsMissed(schedule.id!);
        }
      }

      return updatedSchedules;
    } catch (e) {
      print('خطأ في تحديث الجرعات المتأخرة: $e');
      return 0;
    }
  }

  // الحصول على الجرعات القادمة مع معلومات الدواء
  Future<List<Map<String, dynamic>>> getUpcomingDosesWithMedicineInfo({int? patientId}) async {
    try {
      List<DosageSchedule> schedules = await _dosageScheduleService.getUpcomingSchedules(patientId: patientId);
      List<Map<String, dynamic>> result = [];

      for (DosageSchedule schedule in schedules) {
        // يمكن إضافة معلومات الدواء هنا من خلال join أو استعلام منفصل
        result.add({
          'schedule': schedule,
          'medicine': null, // سيتم تحديثه لاحقاً
        });
      }

      return result;
    } catch (e) {
      print('خطأ في الحصول على الجرعات القادمة: $e');
      return [];
    }
  }

  // حذف جدولة دواء كاملة
  Future<bool> deleteMedicineSchedule(int medicineId) async {
    try {
      // الحصول على الجدولة قبل الحذف لإلغاء الإشعارات
      List<DosageSchedule> schedules = await _dosageScheduleService.getSchedulesByMedicineId(medicineId);

      // إلغاء الإشعارات
      await _notificationService.cancelMedicineNotifications(schedules);

      // حذف جدولة الجرعات
      await _dosageScheduleService.deleteSchedulesByMedicineId(medicineId);

      // حذف سجلات الجرعات
      await _doseLogService.deleteDoseLogsByMedicineId(medicineId);

      return true;
    } catch (e) {
      print('خطأ في حذف جدولة الدواء: $e');
      return false;
    }
  }

  // اقتراح أوقات مناسبة للجرعات
  static List<DateTime> suggestDoseTimes(DateTime firstDose, int dosesPerDay) {
    List<DateTime> suggestions = [];
    int hoursBetween = calculateHoursBetweenDoses(dosesPerDay);

    for (int i = 0; i < dosesPerDay; i++) {
      DateTime doseTime = firstDose.add(Duration(hours: hoursBetween * i));
      suggestions.add(doseTime);
    }

    return suggestions;
  }

  // التحقق من تضارب الأوقات مع أدوية أخرى
  Future<bool> checkTimeConflicts(int patientId, DateTime proposedTime, {int? excludeMedicineId}) async {
    try {
      // يمكن تطوير هذه الوظيفة للتحقق من تضارب الأوقات
      // مع أدوية أخرى للمريض نفسه
      return false; // لا يوجد تضارب
    } catch (e) {
      print('خطأ في التحقق من تضارب الأوقات: $e');
      return false;
    }
  }
}
