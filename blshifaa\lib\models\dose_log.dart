class DoseLog {
  final int? id;
  final int medicineId;
  final String medicineName;
  final int patientId;
  final DateTime scheduledDateTime;
  final DateTime? actualDateTime;
  final String status; // 'taken', 'missed', 'pending'
  final DateTime createdAt;

  DoseLog({
    this.id,
    required this.medicineId,
    required this.medicineName,
    required this.patientId,
    required this.scheduledDateTime,
    this.actualDateTime,
    required this.status,
    required this.createdAt,
  });

  // تحويل من Map إلى DoseLog
  factory DoseLog.fromMap(Map<String, dynamic> map) {
    return DoseLog(
      id: map['id'],
      medicineId: map['medicine_id'],
      medicineName: map['medicine_name'],
      patientId: map['patient_id'],
      scheduledDateTime: DateTime.parse(map['scheduled_datetime']),
      actualDateTime: map['actual_datetime'] != null 
          ? DateTime.parse(map['actual_datetime']) 
          : null,
      status: map['status'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // تحويل من DoseLog إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'medicine_id': medicineId,
      'medicine_name': medicineName,
      'patient_id': patientId,
      'scheduled_datetime': scheduledDateTime.toIso8601String(),
      'actual_datetime': actualDateTime?.toIso8601String(),
      'status': status,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // التحقق من تأخر الجرعة
  bool get isLate {
    if (status != 'taken' || actualDateTime == null) return false;
    return actualDateTime!.isAfter(scheduledDateTime.add(Duration(minutes: 15)));
  }

  // حساب مدة التأخير
  Duration? get delayDuration {
    if (status != 'taken' || actualDateTime == null) return null;
    if (!isLate) return null;
    return actualDateTime!.difference(scheduledDateTime);
  }

  // نسخ مع تعديل بعض الخصائص
  DoseLog copyWith({
    int? id,
    int? medicineId,
    String? medicineName,
    int? patientId,
    DateTime? scheduledDateTime,
    DateTime? actualDateTime,
    String? status,
    DateTime? createdAt,
  }) {
    return DoseLog(
      id: id ?? this.id,
      medicineId: medicineId ?? this.medicineId,
      medicineName: medicineName ?? this.medicineName,
      patientId: patientId ?? this.patientId,
      scheduledDateTime: scheduledDateTime ?? this.scheduledDateTime,
      actualDateTime: actualDateTime ?? this.actualDateTime,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'DoseLog{id: $id, medicineName: $medicineName, status: $status, scheduledDateTime: $scheduledDateTime}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DoseLog &&
        other.id == id &&
        other.medicineId == medicineId &&
        other.scheduledDateTime == scheduledDateTime;
  }

  @override
  int get hashCode {
    return id.hashCode ^ medicineId.hashCode ^ scheduledDateTime.hashCode;
  }
}
