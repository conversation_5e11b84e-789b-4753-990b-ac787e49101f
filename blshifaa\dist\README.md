# 🏥 تطبيق بالشفا - ملفات التوزيع

## 📦 محتويات المجلد

### 🪟 نسخة Windows
- **المجلد**: `blshifaa-windows/`
- **الملف التنفيذي**: `blshifaa.exe`
- **المتطلبات**: Windows 10 أو أحدث
- **الحجم**: ~50 MB

### 🌐 نسخة الويب
- **المجلد**: `blshifaa-web/`
- **الملف الرئيسي**: `index.html`
- **النوع**: Progressive Web App (PWA)
- **المتطلبات**: متصفح حديث

---

## 🚀 طريقة التشغيل

### Windows:
1. فتح مجلد `blshifaa-windows`
2. تشغيل `blshifaa.exe`
3. السماح للتطبيق بالوصول للشبكة (إذا طُلب)

### الويب:
1. فتح مجلد `blshifaa-web`
2. تشغيل خادم ويب محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # أو باستخدام Node.js
   npx serve .
   
   # أو باستخدام PHP
   php -S localhost:8000
   ```
3. فتح المتصفح على `http://localhost:8000`

### نشر الويب:
- رفع محتويات `blshifaa-web` إلى خادم الويب
- التأكد من دعم HTTPS للميزات المتقدمة
- إعداد Service Worker للعمل بدون إنترنت

---

## 📱 الميزات المتاحة

### ✅ جميع النسخ:
- إدارة المرضى والأدوية
- جدولة الجرعات
- سجل الجرعات المتناولة
- واجهة عربية كاملة
- تصميم عصري ومتجاوب

### 🪟 نسخة Windows فقط:
- إشعارات نظام Windows
- تكامل مع شريط المهام
- عمل في الخلفية

### 🌐 نسخة الويب فقط:
- عمل على جميع الأنظمة
- تحديثات تلقائية
- مشاركة سهلة
- عمل بدون إنترنت (بعد التحميل الأولي)

---

## 🔧 استكشاف الأخطاء

### Windows:
- **لا يعمل التطبيق**: تأكد من Windows 10+
- **رسالة أمان**: اختر "تشغيل على أي حال"
- **بطء في التشغيل**: انتظر قليلاً في أول تشغيل

### الويب:
- **لا يحمل التطبيق**: تأكد من تشغيل خادم ويب
- **مشاكل في الخط**: تأكد من دعم UTF-8
- **لا تعمل الإشعارات**: اسمح بالإشعارات في المتصفح

---

## 📞 الدعم

للحصول على المساعدة:
- مراجعة ملف `DEPLOYMENT_GUIDE.md`
- مراجعة ملف `RELEASE_NOTES.md`
- التواصل مع فريق التطوير

---

## 📄 الترخيص

هذا التطبيق مرخص تحت رخصة MIT.

**تطبيق بالشفا - رفيقك الذكي للعناية بصحتك** 💙
