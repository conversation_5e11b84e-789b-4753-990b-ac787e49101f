import 'package:sqflite/sqflite.dart';
import '../models/models.dart';
import 'database_helper.dart';

class MedicineService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة دواء جديد
  Future<int> addMedicine(Medicine medicine) async {
    final db = await _databaseHelper.database;
    return await db.insert('medicines', medicine.toMap());
  }

  // الحصول على جميع أدوية مريض معين
  Future<List<Medicine>> getMedicinesByPatientId(int patientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medicines',
      where: 'patient_id = ? AND is_active = 1',
      whereArgs: [patientId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Medicine.fromMap(maps[i]));
  }

  // الحصول على دواء بالمعرف
  Future<Medicine?> getMedicineById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medicines',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Medicine.fromMap(maps.first);
    }
    return null;
  }

  // تحديث دواء
  Future<int> updateMedicine(Medicine medicine) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'medicines',
      medicine.toMap(),
      where: 'id = ?',
      whereArgs: [medicine.id],
    );
  }

  // حذف دواء (تعطيل)
  Future<int> deactivateMedicine(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'medicines',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // حذف دواء نهائياً
  Future<int> deleteMedicine(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'medicines',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على الأدوية النشطة
  Future<List<Medicine>> getActiveMedicines() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medicines',
      where: 'is_active = 1',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Medicine.fromMap(maps[i]));
  }

  // الحصول على الأدوية المنتهية الصلاحية
  Future<List<Medicine>> getExpiredMedicines() async {
    final db = await _databaseHelper.database;
    final now = DateTime.now().toIso8601String();
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM medicines 
      WHERE is_active = 1 
      AND treatment_duration_days IS NOT NULL 
      AND datetime(first_dose_datetime, '+' || treatment_duration_days || ' days') < ?
      ORDER BY created_at DESC
    ''', [now]);
    return List.generate(maps.length, (i) => Medicine.fromMap(maps[i]));
  }

  // البحث في الأدوية
  Future<List<Medicine>> searchMedicines(String query, {int? patientId}) async {
    final db = await _databaseHelper.database;
    String whereClause = 'name LIKE ? AND is_active = 1';
    List<dynamic> whereArgs = ['%$query%'];
    
    if (patientId != null) {
      whereClause += ' AND patient_id = ?';
      whereArgs.add(patientId);
    }
    
    final List<Map<String, dynamic>> maps = await db.query(
      'medicines',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Medicine.fromMap(maps[i]));
  }

  // عدد الأدوية لمريض معين
  Future<int> getMedicinesCountByPatientId(int patientId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM medicines WHERE patient_id = ? AND is_active = 1',
      [patientId],
    );
    return result.first['count'] as int;
  }
}
