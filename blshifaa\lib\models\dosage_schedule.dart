class DosageSchedule {
  final int? id;
  final int medicineId;
  final DateTime scheduledDateTime;
  final bool isTaken;
  final DateTime? takenDateTime;
  final bool isMissed;
  final DateTime createdAt;

  DosageSchedule({
    this.id,
    required this.medicineId,
    required this.scheduledDateTime,
    this.isTaken = false,
    this.takenDateTime,
    this.isMissed = false,
    required this.createdAt,
  });

  // تحويل من Map إلى DosageSchedule
  factory DosageSchedule.fromMap(Map<String, dynamic> map) {
    return DosageSchedule(
      id: map['id'],
      medicineId: map['medicine_id'],
      scheduledDateTime: DateTime.parse(map['scheduled_datetime']),
      isTaken: map['is_taken'] == 1,
      takenDateTime: map['taken_datetime'] != null 
          ? DateTime.parse(map['taken_datetime']) 
          : null,
      isMissed: map['is_missed'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // تحويل من DosageSchedule إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'medicine_id': medicineId,
      'scheduled_datetime': scheduledDateTime.toIso8601String(),
      'is_taken': isTaken ? 1 : 0,
      'taken_datetime': takenDateTime?.toIso8601String(),
      'is_missed': isMissed ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // التحقق من تأخر الجرعة
  bool get isOverdue {
    if (isTaken) return false;
    return DateTime.now().isAfter(scheduledDateTime.add(Duration(hours: 1)));
  }

  // التحقق من قرب موعد الجرعة (خلال 15 دقيقة)
  bool get isDue {
    if (isTaken) return false;
    final now = DateTime.now();
    final timeDifference = scheduledDateTime.difference(now);
    return timeDifference.inMinutes <= 15 && timeDifference.inMinutes >= 0;
  }

  // حالة الجرعة
  String get status {
    if (isTaken) return 'taken';
    if (isMissed || isOverdue) return 'missed';
    return 'pending';
  }

  // نسخ مع تعديل بعض الخصائص
  DosageSchedule copyWith({
    int? id,
    int? medicineId,
    DateTime? scheduledDateTime,
    bool? isTaken,
    DateTime? takenDateTime,
    bool? isMissed,
    DateTime? createdAt,
  }) {
    return DosageSchedule(
      id: id ?? this.id,
      medicineId: medicineId ?? this.medicineId,
      scheduledDateTime: scheduledDateTime ?? this.scheduledDateTime,
      isTaken: isTaken ?? this.isTaken,
      takenDateTime: takenDateTime ?? this.takenDateTime,
      isMissed: isMissed ?? this.isMissed,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'DosageSchedule{id: $id, medicineId: $medicineId, scheduledDateTime: $scheduledDateTime, isTaken: $isTaken, isMissed: $isMissed}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DosageSchedule &&
        other.id == id &&
        other.medicineId == medicineId &&
        other.scheduledDateTime == scheduledDateTime;
  }

  @override
  int get hashCode {
    return id.hashCode ^ medicineId.hashCode ^ scheduledDateTime.hashCode;
  }
}
