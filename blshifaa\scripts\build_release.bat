@echo off
chcp 65001 >nul
echo 🚀 بدء عملية بناء تطبيق بالشفا...
echo 🚀 Starting Blshifaa App Build Process...

REM تنظيف المشروع
echo 🧹 تنظيف المشروع...
flutter clean
flutter pub get

REM بناء نسخة الويب
echo 🌐 بناء نسخة الويب...
flutter build web --release
if %errorlevel% equ 0 (
    echo ✅ تم بناء نسخة الويب بنجاح
    echo 📁 الملفات في: build\web\
) else (
    echo ❌ فشل في بناء نسخة الويب
)

REM بناء APK للأندرويد
echo 📱 بناء APK للأندرويد...
flutter build apk --release
if %errorlevel% equ 0 (
    echo ✅ تم بناء APK بنجاح
    echo 📁 الملف في: build\app\outputs\flutter-apk\app-release.apk
) else (
    echo ❌ فشل في بناء APK
)

REM بناء App Bundle للأندرويد
echo 📦 بناء App Bundle للأندرويد...
flutter build appbundle --release
if %errorlevel% equ 0 (
    echo ✅ تم بناء App Bundle بنجاح
    echo 📁 الملف في: build\app\outputs\bundle\release\app-release.aab
) else (
    echo ❌ فشل في بناء App Bundle
)

REM بناء نسخة Windows
echo 🪟 بناء نسخة Windows...
flutter build windows --release
if %errorlevel% equ 0 (
    echo ✅ تم بناء نسخة Windows بنجاح
    echo 📁 الملفات في: build\windows\x64\runner\Release\
) else (
    echo ❌ فشل في بناء نسخة Windows
)

REM عرض ملخص النتائج
echo.
echo 📊 ملخص النتائج:
echo ==================

if exist "build\web\index.html" (
    echo ✅ نسخة الويب: متاحة
)

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ APK للأندرويد: متاح
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo ✅ App Bundle للأندرويد: متاح
)

if exist "build\windows\x64\runner\Release\blshifaa.exe" (
    echo ✅ نسخة Windows: متاحة
)

echo.
echo 🎉 انتهت عملية البناء!
echo 🎉 Build process completed!

REM إنشاء مجلد للتوزيع
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set "DIST_DIR=dist\%datestamp%"
mkdir "%DIST_DIR%" 2>nul

REM نسخ الملفات المبنية
if exist "build\web" (
    xcopy "build\web" "%DIST_DIR%\web\" /E /I /Q
    echo 📁 نسخة الويب منسوخة إلى: %DIST_DIR%\web
)

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    copy "build\app\outputs\flutter-apk\app-release.apk" "%DIST_DIR%\blshifaa-android.apk" >nul
    echo 📁 APK منسوخ إلى: %DIST_DIR%\blshifaa-android.apk
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    copy "build\app\outputs\bundle\release\app-release.aab" "%DIST_DIR%\blshifaa-android.aab" >nul
    echo 📁 App Bundle منسوخ إلى: %DIST_DIR%\blshifaa-android.aab
)

if exist "build\windows\x64\runner\Release" (
    xcopy "build\windows\x64\runner\Release" "%DIST_DIR%\windows\" /E /I /Q
    echo 📁 نسخة Windows منسوخة إلى: %DIST_DIR%\windows
)

echo.
echo 📦 جميع الملفات متاحة في: %DIST_DIR%
echo 📦 All files available in: %DIST_DIR%

pause
